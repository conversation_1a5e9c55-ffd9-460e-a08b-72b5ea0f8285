use std::collections::{HashMap, HashSet};
use std::sync::{Arc};
use std::thread::sleep;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use agent_common_service::model::chat_model::{ ChatRelatedRequestBean};
use agent_db::config::runtime_config::AGENT_CONFIG;
use agent_db::dal::remote_client::{search_rerank, IndexTypeEnum, QueryChangeDetail, QueryChangeRequestBean};
use agent_db::domain::code_chat_domain::{ChatRelatedCodeModel, CodefuseChunk, CodefuseFile, CodefuseMethod};
use agent_db::remote::rpc_model::build_success_response;
use agent_db::tools::common_tools::LINE_ENDING;
use lazy_static::lazy_static;
use log::{error, info};
use once_cell::sync::Lazy;
use regex::Regex;
use serde_json::Value;
use tokio::sync::Mutex;
use crate::dialogue::codefuse_index_repository::{query_index_entry, FILE_CLIET};
use crate::dialogue::data_struct::{QueryChatItemResult, QueryChatRelatedData, QueryIndexRequest};
use crate::dialogue::misc_util::{convert_from_codefusechunk_to_model, convert_from_codefusemethod_to_model, doc_to_file, get_simplify_method_vec, query_method_list, CLASS_REGEX};
use crate::function::chatflow::chat_flow_task::{ChatFlowContext, TaskNode, TaskNodeEnum, CONTEXT_QUERY_CHANGE_KEY};
use crate::function::chatflow::understand_query_task::UnderStandQueryNode;

//query检索
pub struct QueryIndexNode {
    status: Arc<Mutex<TaskNodeEnum>>,
}
impl QueryIndexNode {
    pub fn new() -> Self {
        QueryIndexNode {
            status: Arc::new(Mutex::new(TaskNodeEnum::READY)),
        }
    }
}


// 使用lazy_static来初始化静态变量
lazy_static! {
    static ref INDEX_TYPE: Mutex<Vec<IndexTypeEnum>> = Mutex::new(Vec::new());
}

// 获取所有元素
// 获取所有元素
pub async fn get_all_data() -> Option<Vec<IndexTypeEnum>> {
    let vec = INDEX_TYPE.lock().await;
    Some(vec.clone())
}

// 更新指定索引的元素
pub async fn update_data(data: Vec<IndexTypeEnum>) -> Result<(), String> {
    let mut vec = INDEX_TYPE.lock().await;
    vec.clear();
    vec.extend(data);
    Ok(())
}




impl TaskNode for QueryIndexNode {
    async fn start(&self, chat_related_request: &ChatRelatedRequestBean, chat_flow_context: &mut ChatFlowContext) -> TaskNodeEnum {

        let current_status =  match is_ready(self.status.clone()).await {
            true => {
                info!("QueryIndexNode ready is true");
                let query = &chat_related_request.query;
                let current_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                //step 1 ：先处理query改写结果，将结果转为两个set。（这里一定有值，如果没有query改写结果，chat_flow_task的understand_query_task阶段会返回false，整个流程报错）
                let question_change_detail_opt = &chat_flow_context.query_change_detail;
                if question_change_detail_opt.is_none() {
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    error!("QueryIndexNode start query_change_detail is none");
                     (TaskNodeEnum::END, end_time-current_time)
                }else{
                    let question_change_detail = question_change_detail_opt.clone().unwrap();
                    // let query_index_types = AGENT_CONFIG.index_query_type.iter().cloned().collect();
                    let query_index_types = get_all_data().await.unwrap().iter().cloned().collect();




                    // let query_result = convert_query_change_result(&chat_related_request.query, question_change_detail);
                    let mut query_key_word_set = HashSet::new();
                    query_key_word_set.insert(chat_related_request.query.clone());
                    let question_set = vec![chat_related_request.query.clone()];
println!("查看 keyword: {:?}",query_key_word_set);
println!("查看 question_set: {:?}",question_set);
                    //step 2：查询索引
                    let mut query_data = query_index_entry(query_key_word_set,
                                                           question_set,
                                                           query_index_types,
                                                           &chat_related_request.userToken.clone().unwrap_or("".to_string()),
                                                           &chat_related_request.projectUrl.clone().unwrap_or("".to_string()),
                                                           &chat_related_request.productType.clone().unwrap_or("".to_string()),
                                                           AGENT_CONFIG.index_search_top_num).await.unwrap();
                    build_extra_data(&mut query_data);
                    //检索结果
                    let result = search_result_to_model(query_data);
                    // let search_result = valid_method_content_container.len();
                    // let mut result:Option<Vec<ChatRelatedCodeModel>> ;
                    // //只有检索结果大于0才进行rerank
                    // if search_result > 0 {
                    //     result = search_rerank(query.clone(),valid_method_content_container.clone()).await.data;
                    // }else{
                    //     result = Some(vec![]);
                    // }

                    if  result.len()>0{
                        error!("search result not empty but search rerank result is none,search result: {:?}",serde_json::to_string(&result).unwrap_or("".to_string()));
                    }
                    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                    chat_flow_context.result = Some(result);
                    //设置成最新状态
                    let mut status_guard = self.status.lock().await;
                    *status_guard = TaskNodeEnum::END;
                    (TaskNodeEnum::END, end_time-current_time)
                }
            }
            false => {
                info!("QueryIndexNode ready is false");
                let status_guard = self.status.lock().await;
                (status_guard.clone(),0 )
            }
        };
        if current_status.1>0{
            chat_flow_context.query_index_time = current_status.1;
        }
        current_status.0
    }
//查询索引暂时不需要超时时间，默认返回false
    async fn is_timeout(&self, current_millis: u128) -> bool {
        false
    }

    async fn reset(&self) {
        let mut status_guard = self.status.lock().await;
        *status_guard = TaskNodeEnum::READY; // 修改状态
    }
}

///判断是否课执行下一步，如果不行，等待500ms再次判断，总计判断3次（1.5s)
async fn is_ready(status: Arc<tokio::sync::Mutex<TaskNodeEnum>>) -> bool {
    for _ in 0..3 {
        // 获取锁并匹配枚举值
        let mut status_guard = status.lock().await;
        match *status_guard {
            TaskNodeEnum::READY => {
                *status_guard = TaskNodeEnum::RUNNING; // 修改状态
                return true; // 修改成功后返回 true
            }
            _ => sleep(Duration::from_millis(500)),
        }
    }
    false
}



//将检索结果填充到返回值
pub fn search_result_to_model(query_data: QueryChatRelatedData) -> Vec<ChatRelatedCodeModel> {
    //存储有效的ChatRelatedCodeModel
    let mut valid_method_content_container = Vec::new();
    //存储已经存在的key, 避免重复添加
    let mut keys = HashSet::<String>::new();
    match query_data.file_content_data {
        Some(file_content_data) => {
            //todo!()
        }
        None => {}
    }
    match query_data.method_content_data {
        Some(method_content_data) => {
            convert_codefusemethod_to_model(method_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.chunk_content_data {
        Some(chunk_content_data) => {
            convert_codefusechunk_to_model(chunk_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.chunk_vector_data {
        Some(chunk_content_data) => {
            convert_codefusechunk_to_model(chunk_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.method_vector_data {
        Some(method_content_data) => {
            convert_codefusemethod_to_model(method_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.file_summary_vector_data {
        Some(_) => {
            // todo!()
        }
        None => {}
    }
    match query_data.method_summary_vector_data {
        Some(method_content_data) => {
            convert_codefusemethod_to_model(method_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    match query_data.chunk_summary_vector_data {
        Some(chunk_content_data) => {
            convert_codefusechunk_to_model(chunk_content_data, &mut valid_method_content_container, &mut keys);
        }
        None => {}
    }
    valid_method_content_container
}



///将query改写结果转换为key_word_set和question_set
pub fn convert_query_change_result(question: &String, query_change_result: QueryChangeDetail) -> (HashSet<String>, HashSet<String>) {
    let mut key_word_set = HashSet::new();
    if let Some(keyword_zh_value) = query_change_result.keyword_zh {
        key_word_set.extend(keyword_zh_value);
    }
    if let Some(keyword_en_value) = query_change_result.keyword_en {
        key_word_set.extend(keyword_en_value);
    }
    let mut question_set = HashSet::new();

    if let Some(question_zh_value) = query_change_result.question_zh {
        question_set.extend(question_zh_value);
    }
    if let Some(question_en_value) = query_change_result.question_en {
        question_set.extend(question_en_value);
    }
    if let Some(question_value) = query_change_result.question {
        question_set.extend(question_value);
    }
    if !question_set.contains(question) && question.trim().len() > 0 {
        question_set.insert(question.clone());
    }
    (key_word_set, question_set)
}


//todo 什么意思
fn query_class_construct(file_url: &String, detail_method_vec: &Vec<CodefuseMethod>, method_info_vec: &Vec<CodefuseMethod>) -> Option<CodefuseFile> {
    let mut query_param = HashMap::new();
    query_param.insert("id".to_string(), file_url.clone());
    let file_vec_result = FILE_CLIET.query(&query_param, 1);

    match file_vec_result {
        Ok(file_data_vec) => {
            if file_data_vec.len() == 1 {
                for file_data in file_data_vec {
                    let file_info = doc_to_file(file_data.0, file_data.1).data;
                    if !file_info.id.ends_with(".java") {
                        continue;
                    }
                    //只有命中class结构声明才去组装class结构
                    if let Some(captures) = Regex::new(CLASS_REGEX).unwrap().captures(&file_info.content) {
                        let mut result = String::new();
                        if file_info.annotate.len() > 0 {
                            result.push_str(&file_info.annotate);
                            result.push_str(LINE_ENDING)
                        }
                        if let Some(class_type) = captures.get(2) {
                            let class_declaration = class_type.as_str();
                            result.push_str(&class_declaration.to_string());
                            result.push_str(LINE_ENDING);
                            //1:detail_method_vec和method_info_vec都是0，说明检索到的chunk没命中任何函数，那么尝试重新查询所有函数

                            if detail_method_vec.len() == 0 && method_info_vec.len() == 0 {
                                let method_vec = query_method_list(&file_info.id, 0, 0);
                                let simple_method_vec = get_simplify_method_vec(&method_vec.1);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            } else {
                                for method_info in detail_method_vec {
                                    result.push_str(&method_info.content);
                                    result.push_str(LINE_ENDING);
                                }
                                let simple_method_vec = get_simplify_method_vec(&method_info_vec);
                                match simple_method_vec {
                                    Some(method_vec) => {
                                        for method in method_vec {
                                            result.push_str(&method.content);
                                            result.push_str(LINE_ENDING);
                                        }
                                    }
                                    None => {
                                        info!("query_class_construct,simple_method_vec is None.file_url:{:?}",file_url);
                                    }
                                }
                            }
                            result.push_str("}");

                            let result = CodefuseFile {
                                id: file_url.clone(),
                                content: result.clone(),
                                summary: "".to_string(),
                                annotate: "".to_string(),
                                project_url: "".to_string(),
                                branch: "".to_string(),
                                hash: "".to_string(),
                                feature: "".to_string(),
                                summary_keyword: Vec::new(),
                                has_summary: 0,
                                has_summary_vector: 0
                            };
                            return Some(result);
                        }
                    }
                }
            } else {
                error!("query_class_construct,size not 1:{:?}，file_url : {}", file_data_vec, file_url)
            }
        }
        Err(e) => {
            error!("query_class_construct error:{:?}",e);
        }
    }
    None
}

//todo 什么意思？
pub fn build_extra_data(query_chat_related_data: &mut QueryChatRelatedData) {
    if let Some(chunk_content_data_value) = &mut query_chat_related_data.chunk_content_data {
        addition_for_codefusechunk_data(chunk_content_data_value);
    }
    if let Some(chunk_content_data_value) = &mut query_chat_related_data.chunk_vector_data {
        addition_for_codefusechunk_data(chunk_content_data_value);
    }

    if let Some(method_content_data_value) = &mut query_chat_related_data.method_content_data {
        addition_for_codefusemethod_data(method_content_data_value);
    }
    if let Some(method_content_data_value) = &mut query_chat_related_data.method_vector_data {
        addition_for_codefusemethod_data(method_content_data_value);
    }
    ///补齐method summary vector的数据
    if let Some(method_content_data_value) = &mut query_chat_related_data.method_summary_vector_data {
        addition_for_codefusemethod_data(method_content_data_value);
    }
    ///补齐chunk summary vector的数据
    if let Some(chunk_content_data_value) = &mut query_chat_related_data.chunk_summary_vector_data {
        addition_for_codefusechunk_data(chunk_content_data_value);
    }
}


///补充查询类型是codefusemethod的数据
pub fn addition_for_codefusemethod_data(method_content_data_value: &mut Vec<QueryChatItemResult<CodefuseMethod>>){
    for method_content_result in method_content_data_value.iter_mut() {
        let item = &method_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        method_content_result.extend_file_result = query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}
///补充查询类型是codefusechunk的数据
pub fn addition_for_codefusechunk_data(chunk_content_data_value: &mut Vec<QueryChatItemResult<CodefuseChunk>>){
    for chunk_content_result in chunk_content_data_value.iter_mut() {
        let item = &chunk_content_result.search_result.data;
        let method_info = query_method_list(&item.file_url, item.start_line, item.end_line);
        let detail_method_vec = &method_info.0;
        let simple_method_vec = &method_info.1;
        if detail_method_vec.len() > 0 {
            chunk_content_result.extend_method_result = Some(detail_method_vec.clone());
        }
        chunk_content_result.extend_file_result = query_class_construct(&item.file_url, detail_method_vec, simple_method_vec);
    }
}

fn convert_codefusemethod_to_model(method_content_data: Vec<QueryChatItemResult<CodefuseMethod>>, valid_method_content_container: &mut Vec<ChatRelatedCodeModel>, keys: &mut HashSet<String>) {
    if method_content_data.len() > 0 {
        for item in method_content_data {
            let codefuse_method = item.search_result.data;
            if !keys.contains(&codefuse_method.id) {
                keys.insert(codefuse_method.id.clone());
                let chat_related_code_model = convert_from_codefusemethod_to_model(codefuse_method);
                valid_method_content_container.push(chat_related_code_model);
            }
        }
    }
}
fn convert_codefusechunk_to_model(chunk_content_data: Vec<QueryChatItemResult<CodefuseChunk>>, valid_method_content_container: &mut Vec<ChatRelatedCodeModel>, keys: &mut HashSet<String>) {
    if chunk_content_data.len() > 0 {
        for item in chunk_content_data {
            match item.extend_method_result {
                Some(extend_method_result) => {
                    if extend_method_result.len() > 0 {
                        for codefuse_method in extend_method_result {
                            let codefuse_method = codefuse_method.clone();
                            if !keys.contains(&codefuse_method.id) {
                                keys.insert(codefuse_method.id.clone());
                                let chat_related_code_model = convert_from_codefusemethod_to_model(codefuse_method);
                                valid_method_content_container.push(chat_related_code_model);
                            }
                        }
                    }
                }
                None => {
                    ///如果没有extend_method_result,用chunk的数据进行替代
                    let codefuse_chunk_clone = item.search_result.data.clone();
                    if !keys.contains(&codefuse_chunk_clone.id) {
                        keys.insert(codefuse_chunk_clone.id.clone());
                        let chat_related_code_model = convert_from_codefusechunk_to_model(codefuse_chunk_clone);
                        valid_method_content_container.push(chat_related_code_model);
                    }
                }
            }
        }
    }
}