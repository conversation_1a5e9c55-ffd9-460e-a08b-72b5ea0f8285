use std::collections::HashMap;
use std::str::FromStr;
use crate::remote::rpc_model::BaseResponse;
use log::{error, info};
use once_cell::sync::Lazy;
use reqwest::{Client, Error, Response};
use serde::de::DeserializeOwned;
use serde::Serialize;
use std::sync::Arc;
use std::time::Duration;
use reqwest::header::{HeaderMap, HeaderName, HeaderValue};
use tokio::runtime::Runtime;

static HTTP_CLIENT: Lazy<Arc<Client>> = Lazy::new(|| Arc::new(reqwest::Client::new()));
// static HTTP_CLIENT: Lazy<Arc<Client>> = Lazy::new(|| Arc::new(reqwest::Client::builder().danger_accept_invalid_certs(true).build().unwrap()));
static HTTP_CLIENT_SYNC: Lazy<Arc<reqwest::blocking::Client>> = Lazy::new(|| Arc::new(reqwest::blocking::Client::new()));
// static HTTP_CLIENT_SYNC: Lazy<Arc<reqwest::blocking::Client>> = Lazy::new(|| Arc::new(reqwest::blocking::Client::builder().danger_accept_invalid_certs(true).build().unwrap()));
pub const DEFAULT_TIMEOUT_MILLIS: u64 = 50000;


/// 通用的HTTP POST请求函数
pub async fn post_request<T, Req>(
    url: &str,
    request_body: &Req,
    time_out: u64,
    error_message: &str,
) -> Option<BaseResponse<T>>
where
    T: DeserializeOwned,
    Req: Serialize,
{
    let http_result = HTTP_CLIENT.post(url)
        .json(request_body)
        .timeout(Duration::from_millis(time_out))
        .send()
        .await;
    if let Err(error) = http_result {
        error!("{}, err={}", error_message, error);
        return None;
    }
    let response = http_result.unwrap();
    let response_text = response.text().await;
    if let Err(error) = response_text {
        error!("{},  err={}", error_message, error);
        return None;
    }
    let text = response_text.unwrap();
    let base_response: Result<BaseResponse<T>, _> = serde_json::from_str(&text);
    if let Err(error) = base_response {
        error!("{}, response_text: {:?}, err={}", error_message, text, error);
        return None;
    }
    Some(base_response.unwrap())
}

pub fn post_request_sync<T, Req>(
    url: &str,
    request_body: &Req,
    time_out: u64,
    error_message: &str,
) -> Option<BaseResponse<T>>
where
    T: DeserializeOwned,
    Req: Serialize,
{

    // 发送 POST 请求
    let response = HTTP_CLIENT_SYNC.post(url)
        .timeout(Duration::from_millis(time_out))
        .json(request_body)
        .send();

    if let Err(error) = response {
        error!("{}, err={}", error_message, error);
        return None;
    }

    let response = response.unwrap();

    // 获取响应文本
    let response_text = response.text();
    if let Err(error) = response_text {
        error!("{}, err={}", error_message, error);
        return None;
    }
    let text = response_text.unwrap();

    // 解析 JSON 响应
    let base_response: Result<BaseResponse<T>, _> = serde_json::from_str(&text);
    if let Err(error) = base_response {
        error!("{}, response_text: {:?}, err={}", error_message, text, error);
        return None;
    }

    Some(base_response.unwrap())
}

//带header的异步请求
pub async fn post_request_with_header<T, Req>(
    url: &str,
    request_body: &Req,
    headers: HashMap<&str, &str>,
    time_out: u64,
    error_message: &str,
) -> Option<T>
where
    T: DeserializeOwned,
    Req: Serialize,
{
    let mut header_map = HeaderMap::new();
    for (key, value) in headers {
        let header_name = HeaderName::from_str(&key).unwrap();
        let header_value = HeaderValue::from_str(&value).unwrap();
        header_map.insert(header_name, header_value);
    }
    let http_result = HTTP_CLIENT.post(url)
        .headers(header_map)
        .json(request_body)
        .timeout(Duration::from_millis(time_out))
        .send()
        .await;
    if let Err(error) = http_result {
        println!("{}, err={}", error_message, error);
        return None;
    }
    match http_result {
        Ok(resp) => {
            if resp.status().is_success() {
                // 尝试解析响应体
                match resp.json::<T>().await {
                    Ok(text) => Some(text), // 成功解析后返回响应体
                    Err(e) => {
                        eprintln!("Failed to deserialize response as {}: {}: {}", std::any::type_name::<T>(), error_message, e);
                        None
                    }
                }
            } else {
                eprintln!("Request failed: {}: {}", error_message, resp.status());
                None
            }
        }
        Err(e) => {
            eprintln!("Request error: {}: {}", error_message, e);
            None
        }
    }

}
